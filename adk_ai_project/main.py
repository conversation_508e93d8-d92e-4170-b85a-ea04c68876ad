# adk_ai_project/main.py
"""
ADK 知深导师 AI Demo 的主入口文件。
负责初始化 Agent、Runner 并运行对话。
"""
import asyncio
import os
from dotenv import load_dotenv

from google.adk.agents import LlmAgent
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.adk.models.lite_llm import LiteLlm
from google.adk.tools import AgentTool
from google.genai import types

from adk_ai_project.agents import (
    before_model_callback,
    after_model_callback,
    CompressionSubAgent,
    SIMPLIFIED_HISTORY_KEY,
)
from adk_ai_project.memory import InMemoryMemoryService

# 加载环境变量
load_dotenv()

# 从环境中获取模型名称
MODEL_NAME = os.getenv("MODEL", "gemini-2.5-flash")  # 默认使用 gemini-1.5-flash

# 定义常量
APP_NAME = "DeepMentorApp"
USER_ID = "DemoUser"
SESSION_ID = "session_001"


def load_file_content(filepath: str) -> str:
    """加载文件内容"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"错误: 文件未找到 - {filepath}")
        return ""


async def call_agent_async(query: str, runner: Runner, user_id: str, session_id: str):
    """调用 Agent 并处理响应"""
    content = types.Content(role='user', parts=[types.Part(text=query)])
    events = runner.run_async(user_id=user_id, session_id=session_id, new_message=content)

    print(f"用户: {query}")
    async for event in events:
        if event.is_final_response():
            final_response = event.content.parts[0].text
            print(f"导师: {final_response}")
            print("-" * 50)


async def main():
    # 1. 加载必要的文本内容
    persona_prompt_path = "real_dialogues/persona-prompt.md"
    knowledge_material_path = "real_dialogues/大脑健身房.md"

    persona_prompt = load_file_content(persona_prompt_path)
    knowledge_material = load_file_content(knowledge_material_path)

    if not all([persona_prompt, knowledge_material]):
        print("未能加载所有必要文件，请检查文件路径和内容。")
        return

    # 2. 初始化 SessionService
    session_service = InMemorySessionService()
    print("InMemorySessionService 已初始化。")

    # 3. 初始化 MemoryService
    memory_service = InMemoryMemoryService()
    # 将知识材料添加到记忆服务中
    memory_service.add_knowledge(knowledge_material)
    print("InMemoryMemoryService 已初始化并加载知识材料。")

    # 4. 创建 Session
    await session_service.create_session(
        app_name=APP_NAME,
        user_id=USER_ID,
        session_id=SESSION_ID,
        state={SIMPLIFIED_HISTORY_KEY: []}
    )
    print(f"Session 已创建: App='{APP_NAME}', User='{USER_ID}', Session='{SESSION_ID}'")

    # 5. 创建 CompressionSubAgent 和 AgentTool
    compression_sub_agent = CompressionSubAgent(
        name="CompressionAgent",
        description="智能对话历史压缩代理，用于减少 Token 消耗"
    )
    compression_tool = AgentTool(agent=compression_sub_agent)
    print("CompressionSubAgent 和 AgentTool 已创建。")

    # 6. 初始化 LlmAgent 并注册回调函数和工具
    # 将 persona_prompt 作为 LLM 的系统指令（知识材料通过 memory_service 提供）
    main_llm_agent = LlmAgent(
        name="DeepMentorAgent",
        model=LiteLlm(model=f"openai/{MODEL_NAME}"),  # 使用 LiteLLM 包装 OpenAI 兼容的模型
        instruction=persona_prompt,  # 只使用 persona，知识材料通过 memory_service 提供
        tools=[compression_tool],  # 添加压缩工具
        before_model_callback=before_model_callback,
        after_model_callback=after_model_callback,
    )
    print("LlmAgent 已初始化并注册回调函数和工具。")

    # 7. 初始化 Runner（集成 memory_service）
    runner = Runner(
        agent=main_llm_agent,
        app_name=APP_NAME,
        session_service=session_service,
        memory_service=memory_service,  # 🔥 集成记忆服务
    )
    print("Runner 已初始化并集成记忆服务。")

    print("\n--- 开始模拟对话 (输入 'exit' 退出) ---")
    while True:
        user_input = input("用户: ")
        if user_input.lower() == 'exit':
            break

        print("\n--------------------------------------------------")

        # 调用 Agent
        await call_agent_async(user_input, runner, USER_ID, SESSION_ID)

        # 获取并打印简化历史，验证状态更新
        current_session = await session_service.get_session(
            app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID
        )
        if current_session:
            current_simplified_history = current_session.state.get(SIMPLIFIED_HISTORY_KEY, [])
            print(f"当前简化历史: {len(current_simplified_history)} 轮对话")
            for i, dialogue in enumerate(current_simplified_history):
                print(f"  轮次 {i+1}: {dialogue}")
        print("=" * 50)


if __name__ == "__main__":
    asyncio.run(main())
