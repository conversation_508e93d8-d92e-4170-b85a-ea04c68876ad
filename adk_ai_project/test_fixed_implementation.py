#!/usr/bin/env python3
"""
测试修复后的 ADK AI 实现
验证以下功能：
1. Token 优化：简化历史是否正确注入到 LLM 请求中
2. CompressionSubAgent：是否作为工具正确集成
3. Memory Service：是否正确集成到 Runner 中
"""
import asyncio
import os
import sys
from dotenv import load_dotenv

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from google.adk.agents import LlmAgent
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.adk.models.lite_llm import LiteLlm
from google.adk.tools import AgentTool
from google.genai import types

from adk_ai_project.agents import (
    before_model_callback,
    after_model_callback,
    CompressionSubAgent,
    SIMPLIFIED_HISTORY_KEY,
)
from adk_ai_project.memory import InMemoryMemoryService

# 加载环境变量
load_dotenv()

# 测试配置
MODEL_NAME = os.getenv("MODEL", "gemini-2.5-flash")
APP_NAME = "TestApp"
USER_ID = "TestUser"
SESSION_ID = "test_session"

def load_test_content():
    """加载测试用的内容"""
    persona_prompt = """你是一位专业的学习导师，专注于帮助用户理解复杂概念。
你的回答应该简洁明了，富有启发性。"""
    
    knowledge_material = """测试知识材料：
1. 学习是一个渐进的过程
2. 理解比记忆更重要
3. 实践是最好的老师"""
    
    return persona_prompt, knowledge_material

async def test_token_optimization():
    """测试 Token 优化功能"""
    print("\n🔍 测试 Token 优化功能...")
    
    persona_prompt, knowledge_material = load_test_content()
    
    # 初始化服务
    session_service = InMemorySessionService()
    memory_service = InMemoryMemoryService()
    memory_service.add_knowledge(knowledge_material)
    
    # 创建会话
    await session_service.create_session(
        app_name=APP_NAME,
        user_id=USER_ID,
        session_id=SESSION_ID,
        state={SIMPLIFIED_HISTORY_KEY: []}
    )
    
    # 创建 CompressionSubAgent 和工具
    compression_sub_agent = CompressionSubAgent(
        name="CompressionAgent",
        description="智能对话历史压缩代理"
    )
    compression_tool = AgentTool(agent=compression_sub_agent)
    
    # 创建主 Agent
    main_agent = LlmAgent(
        name="TestAgent",
        model=LiteLlm(model=f"openai/{MODEL_NAME}"),
        instruction=persona_prompt,
        tools=[compression_tool],
        before_model_callback=before_model_callback,
        after_model_callback=after_model_callback,
    )
    
    # 创建 Runner
    runner = Runner(
        agent=main_agent,
        app_name=APP_NAME,
        session_service=session_service,
        memory_service=memory_service,
    )
    
    print("✅ 所有组件初始化成功")
    
    # 模拟多轮对话来测试 Token 优化
    test_queries = [
        "什么是有效学习？",
        "如何提高记忆力？",
        "实践的重要性是什么？"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n--- 第 {i} 轮对话 ---")
        print(f"用户: {query}")
        
        # 发送查询
        content = types.Content(role='user', parts=[types.Part(text=query)])
        events = runner.run_async(user_id=USER_ID, session_id=SESSION_ID, new_message=content)
        
        # 处理响应
        async for event in events:
            if event.is_final_response():
                response = event.content.parts[0].text
                print(f"导师: {response[:100]}...")
                break
        
        # 检查会话状态
        session = await session_service.get_session(
            app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID
        )
        if session:
            simplified_history = session.state.get(SIMPLIFIED_HISTORY_KEY, [])
            print(f"📊 当前简化历史: {len(simplified_history)} 轮对话")
            
            # 如果有简化历史，显示最新一轮
            if simplified_history:
                latest = simplified_history[-1]
                print(f"   最新压缩: {latest['user_input'][:50]}...")
                print(f"   最新回复: {latest['assistant_response'][:50]}...")
    
    print("\n✅ Token 优化测试完成")

async def test_compression_tool():
    """测试 CompressionSubAgent 的压缩功能"""
    print("\n🔧 测试 CompressionSubAgent 压缩功能...")

    # 创建 CompressionSubAgent
    compression_agent = CompressionSubAgent(
        name="CompressionAgent",
        description="智能对话历史压缩代理"
    )

    # 测试压缩方法
    test_user_input = "我想了解什么是有效学习？能给我一些具体的建议吗？"
    test_ai_response = """有效学习是指能够高效获取、理解和应用知识的学习方式。以下是一些具体建议：

1. 主动学习：不要被动接受信息，要主动思考和提问
2. 分块学习：将复杂内容分解为小块，逐步掌握
3. 定期复习：遵循遗忘曲线，及时复习巩固
4. 实践应用：理论结合实践，在实际中验证所学
5. 建立联系：将新知识与已有知识建立联系

记住，学习是一个渐进的过程，需要耐心和坚持。"""

    # 测试简单压缩（无历史）
    print("🔄 测试无历史压缩...")
    try:
        compressed_result = compression_agent.compress_dialogue_round(
            test_user_input, test_ai_response, []
        )
        print(f"✅ 压缩成功:")
        print(f"   用户: {compressed_result['user_input'][:80]}...")
        print(f"   AI: {compressed_result['assistant_response'][:80]}...")
    except Exception as e:
        print(f"⚠️  LLM 压缩失败，使用简单压缩: {e}")
        compressed_result = compression_agent._simple_compression_fallback(
            test_user_input, test_ai_response
        )
        print(f"✅ 简单压缩结果:")
        print(f"   用户: {compressed_result['user_input']}")
        print(f"   AI: {compressed_result['assistant_response']}")

    # 测试带历史的压缩
    print("\n🔄 测试带历史压缩...")
    existing_history = [compressed_result]  # 使用上一轮的结果作为历史

    test_user_input_2 = "那么如何提高记忆力呢？"
    test_ai_response_2 = "提高记忆力的方法包括：1. 充足睡眠 2. 规律运动 3. 健康饮食 4. 记忆技巧如联想法"

    try:
        compressed_result_2 = compression_agent.compress_dialogue_round(
            test_user_input_2, test_ai_response_2, existing_history
        )
        print(f"✅ 带历史压缩成功:")
        print(f"   用户: {compressed_result_2['user_input'][:80]}...")
        print(f"   AI: {compressed_result_2['assistant_response'][:80]}...")
    except Exception as e:
        print(f"⚠️  LLM 压缩失败，使用简单压缩: {e}")
        compressed_result_2 = compression_agent._simple_compression_fallback(
            test_user_input_2, test_ai_response_2
        )
        print(f"✅ 简单压缩结果:")
        print(f"   用户: {compressed_result_2['user_input']}")
        print(f"   AI: {compressed_result_2['assistant_response']}")

    print("✅ CompressionSubAgent 压缩测试完成")

async def test_memory_service():
    """测试 Memory Service 集成"""
    print("\n💾 测试 Memory Service 集成...")
    
    # 创建 Memory Service
    memory_service = InMemoryMemoryService()
    
    # 添加测试知识
    test_knowledge = [
        "知识点1：学习需要持续的努力",
        "知识点2：理解比记忆更重要",
        "知识点3：实践是检验真理的唯一标准"
    ]
    
    for knowledge in test_knowledge:
        memory_service.add_knowledge(knowledge)
    
    # 测试获取知识
    all_knowledge = memory_service.get_all_knowledge()
    print(f"📚 存储的知识条目: {len(all_knowledge)}")
    
    # 测试获取上下文文本
    context_text = memory_service.get_context_text(max_items=2)
    print(f"📖 上下文文本长度: {len(context_text)} 字符")
    print(f"   内容预览: {context_text[:100]}...")
    
    print("✅ Memory Service 测试完成")

async def main():
    """主测试函数"""
    print("🚀 开始测试修复后的 ADK AI 实现")
    print("=" * 50)
    
    try:
        # 测试 Memory Service
        await test_memory_service()
        
        # 测试 CompressionSubAgent 工具
        await test_compression_tool()
        
        # 测试 Token 优化（需要 API 密钥）
        if os.getenv("OPENAI_API_KEY"):
            await test_token_optimization()
        else:
            print("\n⚠️  跳过 Token 优化测试（缺少 OPENAI_API_KEY）")
        
        print("\n🎉 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
